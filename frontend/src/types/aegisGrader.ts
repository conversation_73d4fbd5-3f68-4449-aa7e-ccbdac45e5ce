// --- Enums ---
export enum GradingStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN PROGRESS',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED'
}

// Answer sheet status enum to match backend
export enum AnswerSheetStatus {
    PENDING = 'pending',
    PROCESSING = 'processing',
    COMPLETED = 'completed',
    ERROR = 'error'
}

// Overall processing status enum
export enum OverallStatus {
    PENDING = 'pending',
    PROCESSING = 'processing',
    COMPLETED = 'completed',
    PARTIAL_FAILURE = 'partial_failure'
}

export interface ScannedPage {
    imageUrl: string; // dataURL
    timestamp: number;
}

export interface TestDetails {
    createdBy: string;
    className: string;
    subject: string;
    date: string; // YYYY-MM-DD
}

export interface TestDocument {
    id: string;
    studentName: string;
    rollNumber: string;
    pdfUrl: string; // Original filename
    timestamp: number;
    // pdfData?: string; // Base64 dataURL - Optional initially
    pdfData?: File // uploaded file object
    className: string;
    uploading?: boolean;
    error?: string; // Add error field per document
    evaluationResult?: {
        evaluation: {
            total_marks: string;
            maximum_possible_marks: string;
            percentage_score: string;
            section: Array<{
                name: string;
                section_marks: string;
                section_possible_marks: string;
                question: Array<{
                    number: string;
                    marks_awarded: string;
                    marks_possible: string;
                    feedback: string;
                    marks_breakdown: {
                        criterion: Array<{
                            _: string;
                            name: string;
                        }>;
                    };
                }> | {
                    number: string;
                    marks_awarded: string;
                    marks_possible: string;
                    feedback: string;
                    marks_breakdown: {
                        criterion: Array<{
                            _: string;
                            name: string;
                        }>;
                    };
                };
            }>;
        };
    };
    // New fields to match backend answerSheets structure
    status?: AnswerSheetStatus;
    processedAt?: Date;
}

// New interface for processing statistics
export interface ProcessingStats {
    totalAnswerSheets: number;
    successfulEvaluations: number;
    failedEvaluations: number;
    completedAt?: Date;
    processingStartedAt?: Date;
    overallStatus: OverallStatus;
}

// New interface for credit information
export interface CreditInfo {
    totalCreditsCharged: number;
    creditsRefunded: number;
    originalTransactionId?: string;
    refundTransactionIds: string[];
}

export interface RubricDocument {
    type: 'rubric' | 'questionPaper';
    pdfUrl: string; // Original filename
    // pdfData: string; // Base64 dataURL
    pdfData: File // uploaded file object
    timestamp: number;
}

export interface TestSubmission {
    id?: string;
    testDetails: TestDetails;
    answerSheets: TestDocument[]; // Should contain pdfData for submission
    questionPaper?: RubricDocument;
    rubric?: RubricDocument;
    gradingProgress?: number; // Optional: 0-100
    results?: any; // To store grading results from backend
    // New fields to match backend model
    processingStats?: ProcessingStats;
    creditInfo?: CreditInfo;
    createdAt?: Date;
    updatedAt?: Date;
}
